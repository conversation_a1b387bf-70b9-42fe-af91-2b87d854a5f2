defmodule Drops.Relation.Schema.Generator do
  @moduledoc """
  Generates Ecto schema module content from database table introspection.

  This module handles the conversion of database table metadata into
  properly formatted Ecto schema definitions with field types, primary keys,
  and other schema attributes.
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{Field, PrimaryKey}

  @doc """
  Generates a complete Ecto schema module as a string.

  ## Parameters

  - `table_name` - The database table name
  - `module_name` - The full module name for the schema
  - `options` - Generation options including repo and other settings

  ## Returns

  A string containing the complete Ecto schema module definition.

  ## Examples

      iex> Generator.generate_schema_module("users", "MyApp.Relations.User", %{repo: "MyApp.Repo"})
      "defmodule MyApp.Relations.User do\\n  use Ecto.Schema\\n\\n  schema \\"users\\" do\\n..."
  """
  @spec generate_schema_module(String.t(), String.t(), map()) :: String.t()
  def generate_schema_module(table_name, module_name, options) do
    repo = Module.concat([options[:repo]])

    # Introspect the table to get schema metadata
    case Drops.Relation.infer_schema(repo, table_name, []) do
      {:ok, drops_schema} ->
        generate_module_content(module_name, table_name, drops_schema)

      {:error, reason} ->
        raise "Failed to introspect table '#{table_name}': #{inspect(reason)}"
    end
  end

  @doc """
  Generates the module content from a Drops.Relation.Schema struct.

  ## Parameters

  - `module_name` - The full module name
  - `table_name` - The database table name
  - `schema` - The Drops.Relation.Schema struct with metadata

  ## Returns

  A string containing the module definition.
  """
  @spec generate_module_content(String.t(), String.t(), Schema.t()) :: String.t()
  def generate_module_content(module_name, table_name, schema) do
    primary_key_attr = generate_primary_key_attribute(schema.primary_key)
    field_definitions = generate_field_definitions(schema.fields)
    association_definitions = generate_association_definitions(schema.associations)

    """
    defmodule #{module_name} do
      use Ecto.Schema

    #{primary_key_attr}  schema "#{table_name}" do
    #{field_definitions}#{association_definitions}    timestamps()
      end
    end
    """
  end

  @doc """
  Generates the @primary_key attribute if needed.

  ## Parameters

  - `primary_key` - The PrimaryKey struct from the schema

  ## Returns

  A string containing the @primary_key attribute or empty string if default.
  """
  @spec generate_primary_key_attribute(PrimaryKey.t()) :: String.t()
  def generate_primary_key_attribute(%PrimaryKey{fields: fields}) do
    case fields do
      # Default case - single :id field
      [%Field{name: :id, ecto_type: :id}] ->
        ""

      # Single field with different name or type
      [%Field{name: name, ecto_type: ecto_type}] when name != :id or ecto_type != :id ->
        "  @primary_key {#{inspect(name)}, #{inspect(ecto_type)}, autogenerate: true}\n"

      # Composite primary key
      multiple_fields when length(multiple_fields) > 1 ->
        field_specs =
          Enum.map(multiple_fields, fn field ->
            "{#{inspect(field.name)}, #{inspect(field.ecto_type)}}"
          end)

        "  @primary_key [#{Enum.join(field_specs, ", ")}]\n"

      # No primary key
      [] ->
        "  @primary_key false\n"
    end
  end

  @doc """
  Generates field definitions for the schema block.

  ## Parameters

  - `fields` - List of Field structs

  ## Returns

  A string containing the field definitions, properly indented.
  """
  @spec generate_field_definitions([Field.t()]) :: String.t()
  def generate_field_definitions(fields) do
    fields
    |> Enum.reject(&is_primary_key_field?/1)
    |> Enum.reject(&is_timestamp_field?/1)
    |> Enum.map(&generate_field_definition/1)
    |> Enum.join("\n")
    |> case do
      "" -> ""
      content -> content <> "\n\n"
    end
  end

  @doc """
  Generates a single field definition.

  ## Parameters

  - `field` - A Field struct

  ## Returns

  A string containing the field definition.
  """
  @spec generate_field_definition(Field.t()) :: String.t()
  def generate_field_definition(%Field{name: name, ecto_type: ecto_type, source: source}) do
    base_definition = "    field #{inspect(name)}, #{format_ecto_type(ecto_type)}"

    # Add source option if different from field name
    if source != name do
      "#{base_definition}, source: #{inspect(source)}"
    else
      base_definition
    end
  end

  @doc """
  Generates association definitions for the schema block.

  ## Parameters

  - `associations` - List of Ecto association structs

  ## Returns

  A string containing the association definitions, properly indented.
  """
  @spec generate_association_definitions([term()]) :: String.t()
  def generate_association_definitions([]), do: ""

  def generate_association_definitions(associations) do
    associations
    |> Enum.map(&generate_association_definition/1)
    |> Enum.join("\n")
    |> case do
      "" -> ""
      content -> "\n" <> content <> "\n"
    end
  end

  @doc """
  Generates a single association definition.

  ## Parameters

  - `association` - An Ecto association struct

  ## Returns

  A string containing the association definition.
  """
  @spec generate_association_definition(term()) :: String.t()
  def generate_association_definition(%Ecto.Association.BelongsTo{
        field: field,
        related: related
      }) do
    "    belongs_to #{inspect(field)}, #{inspect(related)}"
  end

  def generate_association_definition(%Ecto.Association.Has{
        field: field,
        related: related,
        cardinality: :one
      }) do
    "    has_one #{inspect(field)}, #{inspect(related)}"
  end

  def generate_association_definition(%Ecto.Association.Has{
        field: field,
        related: related,
        cardinality: :many
      }) do
    "    has_many #{inspect(field)}, #{inspect(related)}"
  end

  def generate_association_definition(%Ecto.Association.ManyToMany{
        field: field,
        related: related,
        join_through: join_through
      }) do
    "    many_to_many #{inspect(field)}, #{inspect(related)}, join_through: #{inspect(join_through)}"
  end

  def generate_association_definition(_association) do
    # Fallback for unknown association types
    "    # Unknown association type"
  end

  # Private helper functions

  defp is_primary_key_field?(%Field{name: :id, ecto_type: :id}), do: true
  defp is_primary_key_field?(_), do: false

  defp is_timestamp_field?(%Field{name: name}) when name in [:inserted_at, :updated_at],
    do: true

  defp is_timestamp_field?(_), do: false

  defp format_ecto_type(ecto_type) when is_atom(ecto_type) do
    inspect(ecto_type)
  end

  defp format_ecto_type({:array, inner_type}) do
    "{:array, #{format_ecto_type(inner_type)}}"
  end

  defp format_ecto_type({:map, _}) do
    ":map"
  end

  defp format_ecto_type(ecto_type) do
    inspect(ecto_type)
  end

  @doc """
  Syncs an existing schema file with new field definitions.

  This function attempts to merge new field definitions into an existing
  schema file while preserving custom code and associations.

  ## Parameters

  - `existing_content` - The current content of the schema file
  - `table_name` - The database table name
  - `schema` - The Drops.Relation.Schema struct with new metadata

  ## Returns

  A string containing the updated module content.
  """
  @spec sync_schema_content(String.t(), String.t(), Schema.t()) :: String.t()
  def sync_schema_content(existing_content, table_name, schema) do
    # For now, this is a simplified implementation that replaces the entire schema block
    # A more sophisticated implementation would parse the AST and merge selectively

    # Extract module name from existing content
    module_name = extract_module_name(existing_content)

    if module_name do
      generate_module_content(module_name, table_name, schema)
    else
      # Fallback if we can't parse the module name
      existing_content
    end
  end

  @doc """
  Extracts the module name from existing schema file content.

  ## Parameters

  - `content` - The file content as a string

  ## Returns

  The module name as a string, or nil if not found.
  """
  @spec extract_module_name(String.t()) :: String.t() | nil
  def extract_module_name(content) do
    case Regex.run(~r/defmodule\s+([A-Za-z0-9_.]+)\s+do/, content) do
      [_, module_name] -> module_name
      _ -> nil
    end
  end
end
