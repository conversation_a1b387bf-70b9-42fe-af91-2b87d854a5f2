defmodule Integration.SchemaGenerationTest do
  use ExUnit.Case, async: false

  import ExUnit.CaptureIO

  @sample_app_dir "test/sample_app"
  @test_schemas_dir "test/sample_app/lib/sample_app/relations"

  setup_all do
    # Set up the sample app environment
    original_dir = File.cwd!()
    
    # Change to sample app directory
    File.cd!(@sample_app_dir)
    
    # Install dependencies
    System.cmd("mix", ["deps.get"], stderr_to_stdout: true)
    
    # Create and migrate database
    System.cmd("mix", ["ecto.create"], stderr_to_stdout: true)
    System.cmd("mix", ["ecto.migrate"], stderr_to_stdout: true)
    
    on_exit(fn ->
      # Clean up
      File.cd!(original_dir)
      
      # Remove generated schemas
      if File.exists?(@test_schemas_dir) do
        File.rm_rf!(@test_schemas_dir)
      end
      
      # Remove database
      db_path = Path.join(@sample_app_dir, "sample_app_dev.db")
      if File.exists?(db_path) do
        File.rm!(db_path)
      end
    end)
    
    :ok
  end

  describe "complete schema generation workflow" do
    test "generates schemas for all tables" do
      # Change to sample app directory for the test
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)
      
      try do
        # Run the schema generation task
        output = capture_io(fn ->
          System.cmd("mix", [
            "drops.relations.gen_schemas", 
            "--app", "SampleApp",
            "--namespace", "SampleApp.Relations"
          ], stderr_to_stdout: true)
        end)
        
        # Check that schemas were generated
        user_schema_path = Path.join(@test_schemas_dir, "user.ex")
        post_schema_path = Path.join(@test_schemas_dir, "post.ex")
        comment_schema_path = Path.join(@test_schemas_dir, "comment.ex")
        
        assert File.exists?(user_schema_path), "User schema should be generated"
        assert File.exists?(post_schema_path), "Post schema should be generated"
        assert File.exists?(comment_schema_path), "Comment schema should be generated"
        
        # Verify user schema content
        user_content = File.read!(user_schema_path)
        assert user_content =~ "defmodule SampleApp.Relations.User do"
        assert user_content =~ "use Ecto.Schema"
        assert user_content =~ ~s(schema "users" do)
        assert user_content =~ "field :email, :string"
        assert user_content =~ "field :first_name, :string"
        assert user_content =~ "field :last_name, :string"
        assert user_content =~ "field :age, :integer"
        assert user_content =~ "field :is_active, :boolean"
        assert user_content =~ "timestamps()"
        
        # Verify post schema content
        post_content = File.read!(post_schema_path)
        assert post_content =~ "defmodule SampleApp.Relations.Post do"
        assert post_content =~ ~s(schema "posts" do)
        assert post_content =~ "field :title, :string"
        assert post_content =~ "field :body, :text"
        assert post_content =~ "field :published, :boolean"
        assert post_content =~ "field :user_id, :id"
        
        # Verify comment schema content
        comment_content = File.read!(comment_schema_path)
        assert comment_content =~ "defmodule SampleApp.Relations.Comment do"
        assert comment_content =~ ~s(schema "comments" do)
        assert comment_content =~ "field :body, :text"
        assert comment_content =~ "field :approved, :boolean"
        assert comment_content =~ "field :user_id, :id"
        assert comment_content =~ "field :post_id, :id"
        
      after
        File.cd!(original_dir)
      end
    end

    test "generates schemas for specific tables only" do
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)
      
      try do
        # Clean up any existing schemas
        if File.exists?(@test_schemas_dir) do
          File.rm_rf!(@test_schemas_dir)
        end
        
        # Run the schema generation task for users table only
        capture_io(fn ->
          System.cmd("mix", [
            "drops.relations.gen_schemas", 
            "--app", "SampleApp",
            "--tables", "users",
            "--namespace", "SampleApp.Relations"
          ], stderr_to_stdout: true)
        end)
        
        # Check that only user schema was generated
        user_schema_path = Path.join(@test_schemas_dir, "user.ex")
        post_schema_path = Path.join(@test_schemas_dir, "post.ex")
        comment_schema_path = Path.join(@test_schemas_dir, "comment.ex")
        
        assert File.exists?(user_schema_path), "User schema should be generated"
        refute File.exists?(post_schema_path), "Post schema should not be generated"
        refute File.exists?(comment_schema_path), "Comment schema should not be generated"
        
      after
        File.cd!(original_dir)
      end
    end

    test "handles sync mode correctly" do
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)
      
      try do
        # First, generate schemas
        capture_io(fn ->
          System.cmd("mix", [
            "drops.relations.gen_schemas", 
            "--app", "SampleApp",
            "--tables", "users",
            "--namespace", "SampleApp.Relations"
          ], stderr_to_stdout: true)
        end)
        
        user_schema_path = Path.join(@test_schemas_dir, "user.ex")
        assert File.exists?(user_schema_path)
        
        # Modify the file to add a custom comment
        original_content = File.read!(user_schema_path)
        modified_content = String.replace(original_content, "use Ecto.Schema", "use Ecto.Schema\n  # Custom comment")
        File.write!(user_schema_path, modified_content)
        
        # Run generation again with sync=true (default)
        capture_io(fn ->
          System.cmd("mix", [
            "drops.relations.gen_schemas", 
            "--app", "SampleApp",
            "--tables", "users",
            "--namespace", "SampleApp.Relations",
            "--sync", "true"
          ], stderr_to_stdout: true)
        end)
        
        # File should still exist and be updated
        assert File.exists?(user_schema_path)
        
        # Run generation with sync=false
        capture_io(fn ->
          System.cmd("mix", [
            "drops.relations.gen_schemas", 
            "--app", "SampleApp",
            "--tables", "users", 
            "--namespace", "SampleApp.Relations",
            "--sync", "false"
          ], stderr_to_stdout: true)
        end)
        
        # File should be overwritten
        final_content = File.read!(user_schema_path)
        assert final_content =~ "defmodule SampleApp.Relations.User do"
        
      after
        File.cd!(original_dir)
      end
    end

    test "handles custom directory and namespace" do
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)
      
      custom_dir = "lib/sample_app/schemas"
      
      try do
        # Clean up any existing files
        if File.exists?(custom_dir) do
          File.rm_rf!(custom_dir)
        end
        
        # Run with custom namespace and directory
        capture_io(fn ->
          System.cmd("mix", [
            "drops.relations.gen_schemas", 
            "--app", "SampleApp",
            "--tables", "users",
            "--namespace", "SampleApp.Schemas",
            "--dir", custom_dir
          ], stderr_to_stdout: true)
        end)
        
        # Check that schema was generated in custom location
        user_schema_path = Path.join(custom_dir, "user.ex")
        assert File.exists?(user_schema_path), "User schema should be generated in custom directory"
        
        # Verify content has correct namespace
        user_content = File.read!(user_schema_path)
        assert user_content =~ "defmodule SampleApp.Schemas.User do"
        
      after
        # Clean up custom directory
        if File.exists?(custom_dir) do
          File.rm_rf!(custom_dir)
        end
        File.cd!(original_dir)
      end
    end
  end

  describe "error handling" do
    test "handles missing database gracefully" do
      original_dir = File.cwd!()
      File.cd!(@sample_app_dir)
      
      try do
        # Remove database
        db_path = "sample_app_dev.db"
        if File.exists?(db_path) do
          File.rm!(db_path)
        end
        
        # Try to generate schemas
        output = capture_io(fn ->
          System.cmd("mix", [
            "drops.relations.gen_schemas", 
            "--app", "SampleApp"
          ], stderr_to_stdout: true)
        end)
        
        # Should handle error gracefully
        assert output =~ "No tables found" or output =~ "Failed to introspect"
        
      after
        File.cd!(original_dir)
      end
    end
  end
end
