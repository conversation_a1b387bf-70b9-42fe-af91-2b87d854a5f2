defmodule SampleApp.MixProject do
  use Mix.Project

  def project do
    [
      app: :sample_app,
      version: "0.1.0",
      elixir: "~> 1.14",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  def application do
    [
      extra_applications: [:logger],
      mod: {SampleApp, []}
    ]
  end

  defp deps do
    [
      {:ecto, "~> 3.10"},
      {:ecto_sqlite3, "~> 0.12"},
      {:drops, path: "../../.."}
    ]
  end
end
